<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>联系人功能简化测试</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        .btn-primary {
            background-color: #007bff;
            color: white;
        }
        .btn-primary:hover {
            background-color: #0056b3;
        }
        .result {
            margin-top: 20px;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>联系人功能简化测试</h1>
        
        <div class="info result">
修复说明：
1. 直接使用contacts模型的基础方法
2. 分步骤保存：先保存联系人，再保存关联关系
3. 避免使用复杂的handleContactLogic方法
4. 使用简单直接的数据库操作
        </div>

        <form id="contactForm">
            <div class="form-group">
                <label for="customer_id">客户ID <span style="color:red;">*</span></label>
                <input type="number" id="customer_id" name="customer_id" value="1" required>
            </div>
            
            <div class="form-group">
                <label for="given_name">姓名 <span style="color:red;">*</span></label>
                <input type="text" id="given_name" name="given_name" placeholder="请输入联系人姓名" required>
            </div>
            
            <div class="form-group">
                <label for="mobile">手机号 <span style="color:red;">*</span></label>
                <input type="tel" id="mobile" name="mobile" placeholder="请输入手机号" required>
            </div>
            
            <div class="form-group">
                <label for="position">职位</label>
                <input type="text" id="position" name="position" placeholder="请输入职位">
            </div>
            
            <div class="form-group">
                <label for="email">邮箱</label>
                <input type="email" id="email" name="email" placeholder="请输入邮箱地址">
            </div>
            
            <div class="form-group">
                <label for="honorific">称谓</label>
                <select id="honorific" name="honorific">
                    <option value="">请选择称谓</option>
                    <option value="先生">先生</option>
                    <option value="女士">女士</option>
                    <option value="总经理">总经理</option>
                    <option value="经理">经理</option>
                    <option value="主管">主管</option>
                    <option value="专员">专员</option>
                </select>
            </div>
            
            <div class="form-group">
                <label>
                    <input type="checkbox" id="is_main" name="is_main" value="1">
                    设为主要联系人
                </label>
            </div>
            
            <button type="button" class="btn btn-primary" onclick="submitForm()">提交测试</button>
            <button type="button" class="btn" onclick="clearForm()">清空表单</button>
        </form>
        
        <div id="result"></div>
    </div>

    <script>
        function showResult(type, message) {
            const resultDiv = document.getElementById('result');
            resultDiv.className = 'result ' + type;
            resultDiv.textContent = message;
        }

        function submitForm() {
            const form = document.getElementById('contactForm');
            const formData = new FormData(form);
            
            // 验证必填字段
            const customerId = formData.get('customer_id');
            const givenName = formData.get('given_name');
            const mobile = formData.get('mobile');
            
            if (!customerId || !givenName || !mobile) {
                showResult('error', '请填写所有必填字段');
                return;
            }
            
            // 验证手机号格式
            const mobilePattern = /^1[3-9]\d{9}$/;
            if (!mobilePattern.test(mobile)) {
                showResult('error', '请输入正确的手机号格式');
                return;
            }
            
            showResult('info', '正在提交...');
            
            fetch('index.php?m=flow&a=input&d=mode_customer&do=addContactAjax', {
                method: 'POST',
                body: formData
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error('HTTP error! status: ' + response.status);
                }
                return response.text().then(text => {
                    try {
                        return JSON.parse(text);
                    } catch (e) {
                        console.error('Invalid JSON response:', text);
                        throw new Error('服务器返回了无效的响应格式:\n' + text);
                    }
                });
            })
            .then(res => {
                if (res && res.success) {
                    showResult('success', '✅ 联系人添加成功！\n消息：' + res.msg);
                    // 可选：清空表单
                    // clearForm();
                } else {
                    showResult('error', '❌ 添加失败\n错误：' + (res && res.msg ? res.msg : '未知错误'));
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showResult('error', '❌ 请求失败\n错误：' + error.message);
            });
        }

        function clearForm() {
            document.getElementById('contactForm').reset();
            document.getElementById('result').innerHTML = '';
        }

        // 生成随机测试数据
        function generateTestData() {
            const names = ['张三', '李四', '王五', '赵六', '钱七', '孙八', '周九', '吴十'];
            const positions = ['经理', '主管', '专员', '总监', '助理', '顾问'];
            const honorifics = ['先生', '女士', '总经理', '经理'];
            
            const randomName = names[Math.floor(Math.random() * names.length)];
            const randomPosition = positions[Math.floor(Math.random() * positions.length)];
            const randomHonorific = honorifics[Math.floor(Math.random() * honorifics.length)];
            const randomMobile = '138' + Math.floor(Math.random() * 100000000).toString().padStart(8, '0');
            
            document.getElementById('given_name').value = randomName;
            document.getElementById('mobile').value = randomMobile;
            document.getElementById('position').value = randomPosition;
            document.getElementById('honorific').value = randomHonorific;
            document.getElementById('email').value = randomName.toLowerCase() + '@example.com';
        }

        // 页面加载时生成测试数据
        document.addEventListener('DOMContentLoaded', function() {
            generateTestData();
        });
    </script>
</body>
</html>

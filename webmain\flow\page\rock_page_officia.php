<?php
/**
*	模块：officia.发文单
*	说明：自定义区域内可写你想要的代码
*	来源：流程模块→表单元素管理→[模块.发文单]→生成列表页
*/
defined('HOST') or die ('not access');
?>
<script>
$(document).ready(function(){
	{params}
	var modenum = 'officia',modename='发文单',isflow=1,modeid='19',atype = params.atype,pnum=params.pnum,modenames='',listname='b2ZmaWNpYWw:';
	if(!atype)atype='';if(!pnum)pnum='';
	var fieldsarr = [],fieldsselarr= [],chufarr= {"base_name":"\u62df\u529e\u4eba","base_deptname":"\u62df\u529e\u4eba\u90e8\u95e8"};
	
	<?php
	include_once('webmain/flow/page/rock_page.php');
	?>
	
//[自定义区域start]

$('#addbtn_{rand}').html('<i class="icon-plus"></i> 拟办');
if(pnum)$('#tdleft_{rand}').hide();

c.sendgongwenjs=function(d){
	js.tanbody('senddw','发给其他单位', 350, 200, {
		html:'<form name="sendform"><div style="padding:10px;" id="senddwdiv"><img src="images/mloading.gif"></div></form>',
		btn:[{text:'确定发送'}]
	});
	
	js.ajax(publicmodeurl(modenum,'getcompanydata'),{},function(ret){
		var str = '',da=ret.data;
		for(var i=0;i<da.length;i++){
			str+='<div><label><input type="checkbox" name="xuanzhe[]" value="'+da[i].id+'">'+da[i].name+'</label></div>';
		}
		str+='<div><textarea placeholder="输入说明" id="shuomidv" class="form-control"></textarea></div>';
		$('#senddwdiv').html(str);
	},'get,json');
	
	$('#senddw_btn0').click(function(){
		c.sendgongwenjsok(d.id);
	});
}
c.sendgongwenjsok=function(id1){
	var da = js.getformdata('sendform');
	if(!da.xuanzhe){
		js.msgerror('请选择单位');
		return;
	}
	da.gwid = id1;
	da.sm = get('shuomidv').value;
	js.loading('发送中...');
	
	js.tanclose('senddw');
	js.ajax(publicmodeurl(modenum,'sendcompanydata'),da,function(ret){
		js.msgok(ret.data);
	},'post,json');
}

//[自定义区域end]
	c.initpagebefore();
	js.initbtn(c);
	var a = $('#view'+modenum+'_{rand}').bootstable(bootparams);
	c.init();
	
});
</script>
<!--SCRIPTend-->
<!--HTMLstart-->
<div>
	<table width="100%">
	<tr>
		<td style="padding-right:10px;" id="tdleft_{rand}" nowrap><button id="addbtn_{rand}" class="btn btn-primary" click="clickwin,0" disabled type="button"><i class="icon-plus"></i> <?=lang('新增')?></button></td>
		
		<td><select class="form-control" style="width:110px;border-top-right-radius:0;border-bottom-right-radius:0;padding:0 2px" id="fields_{rand}"></select></td>
		<td><select class="form-control" style="width:60px;border-radius:0px;border-left:0;padding:0 2px" id="like_{rand}"><option value="0"><?=lang('包含')?></option><option value="1"><?=lang('等于')?></option><option value="2"><?=lang('大于')?><?=lang('等于')?></option><option value="3"><?=lang('小于')?><?=lang('等于')?></option><option value="4"><?=lang('不包含')?></option></select></td>
		<td><select class="form-control" style="width:130px;border-radius:0;border-left:0;display:none;padding:0 5px" id="selkey_{rand}"><option value="">-<?=lang('请选择')?>-</option></select><input class="form-control" style="width:130px;border-radius:0;border-left:0;padding:0 5px" id="keygj_{rand}" placeholder="<?=lang('关键字')?>"><input class="form-control" style="width:130px;border-radius:0;border-left:0;padding:0 5px;display:none;" id="key_{rand}" placeholder="<?=lang('关键字')?>/<?=lang('申请人')?>/<?=lang('单号')?>">
		</td>
		<td><select class="form-control" style="width:120px;border-left:0;border-radius:0;" id="selstatus_{rand}"><option value="">-<?=lang('全部')?><?=lang('状态')?>-</option><option style="color:blue" value="0"><?=lang('待处理')?></option><option style="color:green" value="1"><?=lang('已审核')?></option><option style="color:red" value="2"><?=lang('不同意')?></option><option style="color:#888888" value="5"><?=lang('已作废')?></option><option style="color:#17B2B7" value="23"><?=lang('退回')?></option></select></td>
		<td>
			<div style="white-space:nowrap">
			<button style="border-right:0;border-radius:0;border-left:0" class="btn btn-default" click="searchbtn" type="button"><?=lang('搜索')?></button><button class="btn btn-default" id="downbtn_{rand}" type="button" style="padding-left:8px;padding-right:8px;border-top-left-radius:0;border-bottom-left-radius:0"><i class="icon-angle-down"></i></button> 
			</div>
		</td>
		<td  width="90%" style="padding-left:10px"><div id="changatype{rand}" class="btn-group"></div></td>
	
		<td align="right" id="tdright_{rand}" nowrap>
			<span style="display:none" id="daoruspan_{rand}"><button class="btn btn-default" click="daoru,1" type="button"><?=lang('导入')?></button>&nbsp;&nbsp;&nbsp;</span><button class="btn btn-default" style="display:none" id="daobtn_{rand}" disabled click="daochu" type="button"><?=lang('导出')?> <i class="icon-angle-down"></i></button> 
		</td>
	</tr>
	</table>
</div>
<div class="blank10"></div>
<div id="viewofficia_{rand}"></div>
<!--HTMLend-->
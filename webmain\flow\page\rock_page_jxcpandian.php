<?php
/**
*	模块：jxcpandian.商品盘点
*	说明：自定义区域内可写你想要的代码
*	来源：流程模块→表单元素管理→[模块.商品盘点]→生成列表页
*/
defined('HOST') or die ('not access');
?>
<script>
$(document).ready(function(){
	{params}
	var modenum = 'jxcpandian',modename='商品盘点',isflow=0,modeid='193',atype = params.atype,pnum=params.pnum,modenames='',listname='anhjZ29vZHM:';
	if(!atype)atype='';if(!pnum)pnum='';
	var fieldsarr = [],fieldsselarr= [],chufarr= [];
	
	<?php
	include_once('webmain/flow/page/rock_page.php');
	?>
	
//[自定义区域start]

c.initpage=function(){
	$('#key_{rand}').parent().before('<td><select style="width:145px;border-radius:0;border-left:0"  class="form-control" id="depotid_{rand}" ><option value="0">-所有仓库-</option></select></td>');
	$('#key_{rand}').parent().before('<td><input onclick="js.datechange(this,\'date\')" style="width:120px;border-radius:0;border-left:0;'+datesss+'" placeholder="截止日期" readonly class="form-control" id="dt_{rand}" ></td>');
}

var loeb = false;
c.onloadbefore=function(d){
	if(!loeb){
		js.setselectdata(get('depotid_{rand}'), d.depotarr,'value');
		$('#depotid_{rand}').change(function(){
			a.setparams({'depotid':this.value}, true);
		});
		loeb=true;
	}
}


c.searchbtn=function(){
	var dt = get('dt_{rand}').value;
	this.search({dt:dt,depotid:get('depotid_{rand}').value});
}

if(pnum=='all'){
	//bootparams.checked=true;
	bootparams.autoLoad=false;

	var shtm = '<table width="100%"><tr valign="top"><td><div style="border:1px #cccccc solid;width:220px"><div id="optionview_{rand}" style="height:400px;overflow:auto;"></div></div></td><td width="8" nowrap><div style="width:8px;overflow:hidden"></div></td><td width="95%"><div id="viewjxcpandian_{rand}"></div></td></tr></table>';
	$('#viewjxcpandian_{rand}').after(shtm).remove();
	c.stable = 'jscbase';
	c.optionview = 'optionview_{rand}';
	c.optionnum = 'jxcbasetype';
	c.title = '商品分类';
	c.rand = '{rand}';
	c.isguanli = false;

	var c = new optionclass(c);

	$('#'+c.optionview+'').css('height',''+(viewheight-120)+'px');
	$('#tdright_{rand}').prepend(c.getbtnstr('所有商品','allshow')+'&nbsp;&nbsp;');
	$('#tdright_{rand}').prepend('<span id="megss{rand}"></span>&nbsp;&nbsp;');
	setTimeout(function(){c.mobj=a},5);//延迟设置，不然不能双击分类搜索

}
$('#tdleft_{rand}').hide();

//[自定义区域end]
	c.initpagebefore();
	js.initbtn(c);
	var a = $('#view'+modenum+'_{rand}').bootstable(bootparams);
	c.init();
	
});
</script>
<!--SCRIPTend-->
<!--HTMLstart-->
<div>
	<table width="100%">
	<tr>
		<td style="padding-right:10px;" id="tdleft_{rand}" nowrap><button id="addbtn_{rand}" class="btn btn-primary" click="clickwin,0" disabled type="button"><i class="icon-plus"></i> <?=lang('新增')?></button></td>
		
		<td><select class="form-control" style="width:110px;border-top-right-radius:0;border-bottom-right-radius:0;padding:0 2px" id="fields_{rand}"></select></td>
		<td><select class="form-control" style="width:60px;border-radius:0px;border-left:0;padding:0 2px" id="like_{rand}"><option value="0"><?=lang('包含')?></option><option value="1"><?=lang('等于')?></option><option value="2"><?=lang('大于')?><?=lang('等于')?></option><option value="3"><?=lang('小于')?><?=lang('等于')?></option><option value="4"><?=lang('不包含')?></option></select></td>
		<td><select class="form-control" style="width:130px;border-radius:0;border-left:0;display:none;padding:0 5px" id="selkey_{rand}"><option value="">-<?=lang('请选择')?>-</option></select><input class="form-control" style="width:130px;border-radius:0;border-left:0;padding:0 5px" id="keygj_{rand}" placeholder="<?=lang('关键字')?>"><input class="form-control" style="width:130px;border-radius:0;border-left:0;padding:0 5px;display:none;" id="key_{rand}" placeholder="<?=lang('关键字')?>">
		</td>
		
		<td>
			<div style="white-space:nowrap">
			<button style="border-right:0;border-radius:0;border-left:0" class="btn btn-default" click="searchbtn" type="button"><?=lang('搜索')?></button><button class="btn btn-default" id="downbtn_{rand}" type="button" style="padding-left:8px;padding-right:8px;border-top-left-radius:0;border-bottom-left-radius:0"><i class="icon-angle-down"></i></button> 
			</div>
		</td>
		<td  width="90%" style="padding-left:10px"><div id="changatype{rand}" class="btn-group"></div></td>
	
		<td align="right" id="tdright_{rand}" nowrap>
			<span style="display:none" id="daoruspan_{rand}"><button class="btn btn-default" click="daoru,1" type="button"><?=lang('导入')?></button>&nbsp;&nbsp;&nbsp;</span><button class="btn btn-default" style="display:none" id="daobtn_{rand}" disabled click="daochu" type="button"><?=lang('导出')?> <i class="icon-angle-down"></i></button> 
		</td>
	</tr>
	</table>
</div>
<div class="blank10"></div>
<div id="viewjxcpandian_{rand}"></div>
<!--HTMLend-->
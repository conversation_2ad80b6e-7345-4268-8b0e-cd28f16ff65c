<?php
/**
*	记账类型修改
*/
defined('HOST') or die ('not access');
?>
<script>
$(document).ready(function(){
	var lxarr = ['finjishoutype','finjizhitype'],obj=[];
	obj[0] = $('#view1_{rand}').bootstable({
		url:publicmodeurl('finjishou','typedata',{num:lxarr[0]}),
		celleditor:true,tablename:'option',
		columns:[{
			text:'名称',dataIndex:'name',align:'left',editor:true
		},{
			text:'关联科目',dataIndex:'explain',align:'left',renderer:function(v,d){
				return ''+v+' <a onclick="optin{rand}.xuankemu(0,'+d.id+')" href="javascript:;"><i class="icon-search"></i>选</a>';
			}
		},{
			text:'排序号',dataIndex:'sort',editor:true,type:'number'
		}]
	});

	obj[1] = $('#view2_{rand}').bootstable({
		url:publicmodeurl('finjishou','typedata',{num:lxarr[1]}),
		celleditor:true,tablename:'option',
		columns:[{
			text:'名称',dataIndex:'name',align:'left',editor:true
		},{
			text:'关联科目',dataIndex:'explain',align:'left',renderer:function(v,d){
				return ''+v+' <a onclick="optin{rand}.xuankemu(1,'+d.id+')" href="javascript:;"><i class="icon-search"></i>选</a>';
			}
		},{
			text:'排序号',dataIndex:'sort',editor:true,type:'number'
		}]
	});
	
	var c = {
		relad:function(o1,lx){
			obj[lx].reload();
		},
		adds:function(o1,lx){
			js.prompt('新增分类','请输入内容',function(jg,txt){
				if(jg=='yes'&&txt)c.addss(txt,lx);
			});
		},
		addss:function(txt,lx){
			js.loading('添加中...');
			js.ajax(publicmodeurl('finjishou','typedataadd'),{num:lxarr[lx],msg:txt}, function(d){
				js.msgok('添加成功');
				obj[lx].reload();
			},'post,json');
		},
		xuankemu:function(lx,id){
			$.selectdata({
				title:'选会计科目',
				url:publicmodeurl('finjishou','kemudata'),
				onselect:function(seld,sna,sid){
					js.loading('保存中...');
					js.ajax(publicmodeurl('finjishou','typedatasavekemu'),{sna:sna,sid:sid,id:id}, function(d){
						js.msgok('保存成功');
						obj[lx].reload();
					},'post,json');
				}
			});
		}
	}
	js.initbtn(c);
	optin{rand} = c;
});
</script>
<table width="100%">
<tr valign="top">
	<td width="50%">
		<div>
			<button class="btn btn-default" click="relad,0" type="button">刷新</button>&nbsp;&nbsp;
			<button class="btn btn-default" click="adds,0" type="button">新增</button>&nbsp;&nbsp;
		</div>
		<div class="blank10"></div>
		<div class="panel panel-info">
			<div class="panel-heading"><h3 class="panel-title">记账收入类型</h3></div>
			<div id="view1_{rand}"></div>
			
		</div>
		<div class="tishi">设置会计科目是为了将记账记录生成凭证</div>
	</td>
	<td width="10"><div style="width:10px;overflow:hidden"></div></td>
	<td width="50%">
		<div>
			<button class="btn btn-default" click="relad,1" type="button">刷新</button>&nbsp;&nbsp;
			<button class="btn btn-default" click="adds,1" type="button">新增</button>&nbsp;&nbsp;
		</div>
		<div class="blank10"></div>
		<div class="panel panel-success">
			<div class="panel-heading"><h3 class="panel-title">记账支出类型</h3></div>
			
			<div id="view2_{rand}"></div>
		</div>
	</td>
</tr>
</table>

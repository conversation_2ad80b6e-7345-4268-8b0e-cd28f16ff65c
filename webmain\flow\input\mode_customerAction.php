<?php
//客户录入
class mode_customerClassAction extends inputAction{
	

	protected function savebefore($table, $arr, $id, $addbo){
		
	}
	
	
	protected function saveafter($table, $arr, $id, $addbo){
		$name = $arr['name'];
		m('custfina')->update("`custname`='$name'", "`custid`='$id'");
		m('custract')->update("`custname`='$name'", "`custid`='$id'");
		m('custsale')->update("`custname`='$name'", "`custid`='$id'");
		m('custappy')->update("`custname`='$name'", "`custid`='$id'");
		m('goodm')->update("`custname`='$name'", "`custid`='$id' and `type` in(1,2)");//1采购,2销售
		m('crm')->custtotal($id);
        m('project')->update("`custname`='$name'", "`custid`='$id'");
        
        // 使用新的统一联系人处理逻辑
        $mobile = isset($arr['tel']) ? trim($arr['tel']) : '';
        $linkname = isset($arr['linkname']) ? trim($arr['linkname']) : '';
        
        if (!empty($mobile) && !empty($linkname)) {
            // 准备额外数据
            $extra_data = [];
            if (isset($arr['linkposition'])) $extra_data['position'] = trim($arr['linkposition']);
            if (isset($arr['linkemail'])) $extra_data['email'] = trim($arr['linkemail']);
            if (isset($arr['linkhonorific'])) $extra_data['honorific'] = trim($arr['linkhonorific']);
            
            // 调用统一的联系人处理逻辑（客户场景）
            $result = m('contacts')->handleContactLogic($mobile, $linkname, 'customer', $id, 0, $this, $extra_data);
            
            if (!$result['success']) {
                // 如果联系人处理失败，可以在这里添加日志或其他处理
            }
        }
	}
	
	// 删除前处理已移至 customerModel.php 的 flowdeletebillbefore 方法中统一处理
	
	/**
	 * 获取客户相关数据的Ajax方法
	 * 支持多个选项卡：销售机会、合同、收款单、付款单、售后单、销售单、客户计划、联系人
	 * @return string 返回格式化的HTML表格数据
	 */
	public function getothernrAjax()
	{
		$custid = (int)$this->get('custid','0');//客户id
		$ind  	= (int)$this->get('ind','0');//第几个选择卡
		$bh   	= 'custsale';//销售机会的
		$atype  = 'all'; //流程模块条件编号
		
		// 根据选项卡索引设置对应的模块和条件
		if($ind==2)$bh='custract';
		if($ind==3){
			$bh='custfina';
			$atype = 'allskd'; //所有收款单的
		}
		if($ind==4){
			$bh='custfinb';
			$atype = 'allfkd';//所有付款单！
		}
		if($ind==5){
			$bh='electwork';//售后单
		}
		if($ind==6){
			$bh='custxiao';//销售单
		}
		if($ind==7){
			$bh='custplan';
		}
		if($ind==8){
			// 新增：客户联系人选项卡
			return $this->getCustomerContactsTable($custid);
		}
			
		//读取数据
		$flow  = m('flow')->initflow($bh);//初始化模块
		//调用方法getrowstable是在webmain\model\flow\flow.php 里面的，
		//第一个参数，流程模块条件的编号，如果没有这个编号是读取不到数据
		//第二个参数，额外添加的条件，下面那说明的跟这个客户有关
		//第3个参数，默认读取的条数，默认是100
		$cont  = $flow->getrowstable($atype, 'and `custid`='.$custid.'');//读取表格数据
		return $cont;
	}
	
	public function shatetoAjax()
	{
		$sna  = $this->post('sna');
		$sid  = c('check')->onlynumber($this->post('sid'));
		$khid = c('check')->onlynumber($this->post('khid'));
		
		m('customer')->update(array(
			'shate' 	=> $sna,
			'shateid' 	=> $sid,
		),"`id` in($khid)");
	}
	
	/**
	 * 获取客户联系人表格数据
	 * @param int $custid 客户ID
	 * @return string 返回格式化的HTML表格数据
	 */
	private function getCustomerContactsTable($custid)
	{
		// 检查客户ID是否有效
		if (empty($custid)) {
			return '<div style="padding: 20px; text-align: center; color: #666;">无效的客户ID</div>';
		}

		// 初始化HTML内容
		$html = '';

		// 添加"添加联系人"按钮
		$html .= '<div style="margin-bottom: 10px; text-align: right;">
			<button type="button" onclick="addCustomerContact()" class="btn btn-primary btn-sm"><i class="fa fa-plus"></i> 添加联系人</button>
		</div>';

		// 弹窗HTML结构
		$html .= <<<HTML
<div id="addContactModal" class="ui-dialog" style="display:none; width:300px; position:fixed; left:50%; top:50%; transform:translate(-50%,-50%); z-index:9999; background:#fff; border-radius:8px; box-shadow:0 2px 16px rgba(0,0,0,0.15);">
    <div class="aui_titleBar" style="padding:12px 20px; border-bottom:1px solid #eee; font-size:18px; font-weight:bold;">
        添加联系人
        <span style="float:right; cursor:pointer; font-size:22px;" onclick="document.getElementById('addContactModal').style.display='none'">&times;</span>
    </div>
    <div class="aui_content" style="padding:24px;">
        <form id="contactForm">
            <div style="margin-bottom:16px;">
                <label>姓名 <span style="color:red">*</span></label>
                <input type="text" name="given_name" class="form-control" required style="width:100%;">
            </div>
            <div style="margin-bottom:16px;">
                <label>手机号 <span style="color:red">*</span></label>
                <input type="text" name="mobile" class="form-control" required style="width:100%;">
            </div>
            <div style="margin-bottom:16px;">
                <label>职位</label>
                <input type="text" name="position" class="form-control" style="width:100%;">
            </div>
            <div style="margin-bottom:16px;">
                <label>邮箱</label>
                <input type="email" name="email" class="form-control" style="width:100%;">
            </div>
            <div style="margin-bottom:16px;">
                <label>称谓</label>
                <input type="text" name="honorific" class="form-control" style="width:100%;">
            </div>
        </form>
    </div>
    <div style="padding:12px 20px; border-top:1px solid #eee; text-align:right;">
        <button type="button" class="btn btn-default" onclick="document.getElementById('addContactModal').style.display='none'">取消</button>
        <button type="button" class="btn btn-primary" onclick="submitContactForm()">直接提交</button>
    </div>
</div>
<script>
var customer_id = {$custid}; // 这里直接输出PHP变量

function addCustomerContact() {
    document.getElementById('addContactModal').style.display = 'block';
}
function submitContactForm() {
    var form = document.getElementById('contactForm');
    var data = new FormData(form);
    data.append('customer_id', customer_id); // 这里用JS变量

    fetch('?m=customer&a=addContactAjax', {
        method: 'POST',
        body: data
    })
    .then(response => response.json())
    .then(res => {
        if(res.success){
            alert('添加成功');
            document.getElementById('addContactModal').style.display = 'none';
            location.reload(); // 或刷新联系人表格
        }else{
            alert(res.msg || '添加失败');
        }
    });
}
</script>
HTML;

		try {
			// 1. 从关联表和联系人表联合查询获取完整信息
			$custid = (int)$custid; // 确保客户ID为整数，防止SQL注入
			$sql = "SELECT c.`given_name`, c.`mobile`, c.`position`, c.`email`, c.`honorific`, cr.`is_main`
					FROM `[Q]contacts` c
					INNER JOIN `[Q]custcontrel` cr ON c.`id` = cr.`contact_id`
					WHERE cr.`customer_id` = $custid AND cr.`rel_type` IN (1,3)
					ORDER BY cr.`is_main` DESC, c.`given_name` ASC";
			$contacts_data = $this->db->getall($sql);

			// 如果没有关联的联系人，则显示提示信息
			if (empty($contacts_data)) {
				return $html . '<div style="padding: 20px; text-align: center; color: #666;">暂无联系人信息，点击上方按钮添加。</div>';
			}

			// 定义表格头部字符串，字段名需与数据key一致
			$headstr = 'xuhaos,序号,center@given_name,姓名,left@mobile,手机号,left@position,职位,left@email,邮箱,left@honorific,称谓,left@is_main,主要联系人,center';

			// 格式化数据用于表格生成，key需与表头字段一致
			$formattedData = array();
			$index = 1;
			foreach ($contacts_data as $contact) {
				$formattedData[] = array(
					'xuhaos'      => $index++,
					'given_name'  => $contact['given_name'] ?: '-',
					'mobile'      => $contact['mobile'] ?: '-',
					'position'    => $contact['position'] ?: '-',
					'email'       => $contact['email'] ?: '-',
					'honorific'   => $contact['honorific'] ?: '-',
					'is_main'     => $contact['is_main'] ? '是' : '否'
				);
			}

			// 使用系统插件 `htmlChajian` 的 `createrows` 方法生成表格
			$tableHtml = c('html')->createrows($formattedData, $headstr);

			return $html . $tableHtml;

		} catch (Exception $e) {
			// 捕获并显示数据库查询或其他异常
			return $html . '<div style="padding: 20px; text-align: center; color: #d32f2f;">获取联系人信息时发生错误：' . $e->getMessage() . '</div>';
		}
	}

	public function addContactAjax()
	{
		$customer_id = (int)$this->get('customer_id');
		$name = trim($this->get('given_name'));
		$mobile = trim($this->get('mobile'));
		$position = trim($this->get('position'));
		$email = trim($this->get('email'));
		$honorific = trim($this->get('honorific'));

		if (!$customer_id || !$name || !$mobile) {
			$this->returnjson(['success'=>false, 'msg'=>'姓名、手机号、客户ID不能为空']);
			return;
		}

		// 新增或更新联系人
		$extra_data = [
			'position' => $position,
			'email' => $email,
			'honorific' => $honorific
		];

		// 使用统一的联系人处理逻辑
		$result = m('contacts')->handleContactLogic($mobile, $name, 'customer', $customer_id, 0, $this, $extra_data);

		if (!$result['success']) {
			$this->returnjson(['success'=>false, 'msg'=>$result['message'] ?: '联系人保存失败']);
			return;
		}

		$this->returnjson(['success'=>true, 'msg'=>'添加成功']);
	}
}
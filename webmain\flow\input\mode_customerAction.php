<?php
//客户录入
class mode_customerClassAction extends inputAction{
	

	protected function savebefore($table, $arr, $id, $addbo){
		
	}
	
	
	protected function saveafter($table, $arr, $id, $addbo){
		$name = $arr['name'];
		m('custfina')->update("`custname`='$name'", "`custid`='$id'");
		m('custract')->update("`custname`='$name'", "`custid`='$id'");
		m('custsale')->update("`custname`='$name'", "`custid`='$id'");
		m('custappy')->update("`custname`='$name'", "`custid`='$id'");
		m('goodm')->update("`custname`='$name'", "`custid`='$id' and `type` in(1,2)");//1采购,2销售
		m('crm')->custtotal($id);
        m('project')->update("`custname`='$name'", "`custid`='$id'");
        
        // 使用新的统一联系人处理逻辑
        $mobile = isset($arr['tel']) ? trim($arr['tel']) : '';
        $linkname = isset($arr['linkname']) ? trim($arr['linkname']) : '';
        
        if (!empty($mobile) && !empty($linkname)) {
            // 准备额外数据
            $extra_data = [];
            if (isset($arr['linkposition'])) $extra_data['position'] = trim($arr['linkposition']);
            if (isset($arr['linkemail'])) $extra_data['email'] = trim($arr['linkemail']);
            if (isset($arr['linkhonorific'])) $extra_data['honorific'] = trim($arr['linkhonorific']);
            
            // 调用统一的联系人处理逻辑（客户场景）
            $result = m('contacts')->handleContactLogic($mobile, $linkname, 'customer', $id, 0, $this, $extra_data);
            
            if (!$result['success']) {
                // 如果联系人处理失败，可以在这里添加日志或其他处理
            }
        }
	}
	
	// 删除前处理已移至 customerModel.php 的 flowdeletebillbefore 方法中统一处理
	
	/**
	 * 获取客户相关数据的Ajax方法
	 * 支持多个选项卡：销售机会、合同、收款单、付款单、售后单、销售单、客户计划、联系人
	 * @return string 返回格式化的HTML表格数据
	 */
	public function getothernrAjax()
	{
		$custid = (int)$this->get('custid','0');//客户id
		$ind  	= (int)$this->get('ind','0');//第几个选择卡
		$bh   	= 'custsale';//销售机会的
		$atype  = 'all'; //流程模块条件编号
		
		// 根据选项卡索引设置对应的模块和条件
		if($ind==2)$bh='custract';
		if($ind==3){
			$bh='custfina';
			$atype = 'allskd'; //所有收款单的
		}
		if($ind==4){
			$bh='custfinb';
			$atype = 'allfkd';//所有付款单！
		}
		if($ind==5){
			$bh='electwork';//售后单
		}
		if($ind==6){
			$bh='custxiao';//销售单
		}
		if($ind==7){
			$bh='custplan';
		}
		if($ind==8){
			// 新增：客户联系人选项卡
			return $this->getCustomerContactsTable($custid);
		}
			
		//读取数据
		$flow  = m('flow')->initflow($bh);//初始化模块
		//调用方法getrowstable是在webmain\model\flow\flow.php 里面的，
		//第一个参数，流程模块条件的编号，如果没有这个编号是读取不到数据
		//第二个参数，额外添加的条件，下面那说明的跟这个客户有关
		//第3个参数，默认读取的条数，默认是100
		$cont  = $flow->getrowstable($atype, 'and `custid`='.$custid.'');//读取表格数据
		return $cont;
	}
	
	public function shatetoAjax()
	{
		$sna  = $this->post('sna');
		$sid  = c('check')->onlynumber($this->post('sid'));
		$khid = c('check')->onlynumber($this->post('khid'));
		
		m('customer')->update(array(
			'shate' 	=> $sna,
			'shateid' 	=> $sid,
		),"`id` in($khid)");
	}
	
	/**
	 * 获取客户联系人表格数据
	 * @param int $custid 客户ID
	 * @return string 返回格式化的HTML表格数据
	 */
	private function getCustomerContactsTable($custid)
	{
		// 检查客户ID是否有效
		if (empty($custid)) {
			return '<div style="padding: 20px; text-align: center; color: #666;">无效的客户ID</div>';
		}

		// 初始化HTML内容
		$html = '';

		// 添加操作按钮区域
		$html .= '<div style="margin-bottom: 15px; display: flex; justify-content: space-between; align-items: center;">
			<div>
				<button type="button" onclick="addCustomerContact()" class="btn btn-primary btn-sm">
					<i class="fa fa-plus"></i> 添加联系人
				</button>
				<button type="button" onclick="refreshContactTable()" class="btn btn-default btn-sm" style="margin-left: 8px;">
					<i class="fa fa-refresh"></i> 刷新
				</button>
			</div>
			<div style="font-size: 12px; color: #666;">
				<i class="fa fa-info-circle"></i> 主要联系人将优先显示
			</div>
		</div>';

		// 优化的弹窗HTML结构
		$html .= $this->getContactModalHtml($custid);

		try {
			return $html . $this->getContactsTableData($custid);
		} catch (Exception $e) {
			// 捕获并显示数据库查询或其他异常
			return $html . '<div style="padding: 20px; text-align: center; color: #d32f2f;">
				<i class="fa fa-exclamation-triangle"></i> 获取联系人信息时发生错误：' . htmlspecialchars($e->getMessage()) . '
			</div>';
		}
	}

	/**
	 * 获取联系人弹窗HTML
	 * @param int $custid 客户ID
	 * @return string
	 */
	private function getContactModalHtml($custid)
	{
		return <<<HTML
<div id="addContactModal" class="ui-dialog" style="display:none; width:420px; position:fixed; left:50%; top:50%; transform:translate(-50%,-50%); z-index:9999; background:#fff; border-radius:8px; box-shadow:0 4px 20px rgba(0,0,0,0.15);">
    <div class="aui_titleBar" style="padding:15px 20px; border-bottom:1px solid #eee; font-size:16px; font-weight:600; background:#f8f9fa; border-radius:8px 8px 0 0;">
        <i class="fa fa-user-plus" style="margin-right:8px; color:#007bff;"></i>添加联系人
        <span style="float:right; cursor:pointer; font-size:20px; color:#666; line-height:1;" onclick="closeContactModal()" title="关闭">&times;</span>
    </div>
    <div class="aui_content" style="padding:20px;">
        <form id="contactForm" onsubmit="return false;">
            <div class="form-row" style="display:flex; gap:15px; margin-bottom:15px;">
                <div style="flex:1;">
                    <label style="display:block; margin-bottom:5px; font-weight:500;">姓名 <span style="color:#dc3545;">*</span></label>
                    <input type="text" name="given_name" class="form-control" required
                           style="width:100%; padding:8px 12px; border:1px solid #ddd; border-radius:4px; font-size:14px;"
                           placeholder="请输入联系人姓名">
                </div>
                <div style="flex:1;">
                    <label style="display:block; margin-bottom:5px; font-weight:500;">手机号 <span style="color:#dc3545;">*</span></label>
                    <input type="tel" name="mobile" class="form-control" required
                           style="width:100%; padding:8px 12px; border:1px solid #ddd; border-radius:4px; font-size:14px;"
                           placeholder="请输入手机号" pattern="^1[3-9]\d{9}$">
                </div>
            </div>
            <div class="form-row" style="display:flex; gap:15px; margin-bottom:15px;">
                <div style="flex:1;">
                    <label style="display:block; margin-bottom:5px; font-weight:500;">职位</label>
                    <input type="text" name="position" class="form-control"
                           style="width:100%; padding:8px 12px; border:1px solid #ddd; border-radius:4px; font-size:14px;"
                           placeholder="请输入职位">
                </div>
                <div style="flex:1;">
                    <label style="display:block; margin-bottom:5px; font-weight:500;">称谓</label>
                    <select name="honorific" class="form-control"
                            style="width:100%; padding:8px 12px; border:1px solid #ddd; border-radius:4px; font-size:14px;">
                        <option value="">请选择称谓</option>
                        <option value="先生">先生</option>
                        <option value="女士">女士</option>
                        <option value="总经理">总经理</option>
                        <option value="经理">经理</option>
                        <option value="主管">主管</option>
                        <option value="专员">专员</option>
                    </select>
                </div>
            </div>
            <div style="margin-bottom:15px;">
                <label style="display:block; margin-bottom:5px; font-weight:500;">邮箱</label>
                <input type="email" name="email" class="form-control"
                       style="width:100%; padding:8px 12px; border:1px solid #ddd; border-radius:4px; font-size:14px;"
                       placeholder="请输入邮箱地址">
            </div>
            <div style="margin-bottom:15px;">
                <label style="display:flex; align-items:center; font-weight:500;">
                    <input type="checkbox" name="is_main" value="1" style="margin-right:8px;">
                    设为主要联系人
                </label>
                <small style="color:#666; margin-left:20px;">主要联系人将在列表中优先显示</small>
            </div>
        </form>
    </div>
    <div style="padding:15px 20px; border-top:1px solid #eee; text-align:right; background:#f8f9fa; border-radius:0 0 8px 8px;">
        <button type="button" class="btn btn-default" onclick="closeContactModal()" style="margin-right:10px;">
            <i class="fa fa-times"></i> 取消
        </button>
        <button type="button" class="btn btn-primary" onclick="submitContactForm()">
            <i class="fa fa-check"></i> 确认添加
        </button>
    </div>
</div>

<script>
var customer_id = {$custid};
var isSubmitting = false;

function addCustomerContact() {
    document.getElementById('addContactModal').style.display = 'block';
    document.querySelector('input[name="given_name"]').focus();
}

function closeContactModal() {
    document.getElementById('addContactModal').style.display = 'none';
    document.getElementById('contactForm').reset();
    isSubmitting = false;
}

function refreshContactTable() {
    location.reload();
}

function validateContactForm() {
    var form = document.getElementById('contactForm');
    var name = form.given_name.value.trim();
    var mobile = form.mobile.value.trim();

    if (!name) {
        alert('请输入联系人姓名');
        form.given_name.focus();
        return false;
    }

    if (!mobile) {
        alert('请输入手机号');
        form.mobile.focus();
        return false;
    }

    // 验证手机号格式
    var mobilePattern = /^1[3-9]\d{9}$/;
    if (!mobilePattern.test(mobile)) {
        alert('请输入正确的手机号格式');
        form.mobile.focus();
        return false;
    }

    // 验证邮箱格式（如果填写了）
    var email = form.email.value.trim();
    if (email) {
        var emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailPattern.test(email)) {
            alert('请输入正确的邮箱格式');
            form.email.focus();
            return false;
        }
    }

    return true;
}

function submitContactForm() {
    if (isSubmitting) return;

    if (!validateContactForm()) return;

    isSubmitting = true;
    var submitBtn = document.querySelector('button[onclick="submitContactForm()"]');
    var originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="fa fa-spinner fa-spin"></i> 提交中...';
    submitBtn.disabled = true;

    var form = document.getElementById('contactForm');
    var data = new FormData(form);
    data.append('customer_id', customer_id);

    fetch('?m=customer&a=addContactAjax', {
        method: 'POST',
        body: data
    })
    .then(response => response.json())
    .then(res => {
        if(res.success){
            alert('联系人添加成功！');
            closeContactModal();
            refreshContactTable();
        } else {
            alert(res.msg || '添加失败，请重试');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('网络错误，请检查网络连接后重试');
    })
    .finally(() => {
        isSubmitting = false;
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    });
}

// 键盘事件处理
document.addEventListener('keydown', function(e) {
    var modal = document.getElementById('addContactModal');
    if (modal.style.display === 'block') {
        if (e.key === 'Escape') {
            closeContactModal();
        } else if (e.key === 'Enter' && e.ctrlKey) {
            submitContactForm();
        }
    }
});
</script>
HTML;
	}

	/**
	 * 获取联系人表格数据
	 * @param int $custid 客户ID
	 * @return string
	 */
	private function getContactsTableData($custid)
	{
		// 确保客户ID为整数，防止SQL注入
		$custid = (int)$custid;

		// 从关联表和联系人表联合查询获取完整信息
		$sql = "SELECT c.`id`, c.`given_name`, c.`mobile`, c.`position`, c.`email`, c.`honorific`, cr.`is_main`
				FROM `[Q]contacts` c
				INNER JOIN `[Q]custcontrel` cr ON c.`id` = cr.`contact_id`
				WHERE cr.`customer_id` = $custid AND cr.`rel_type` IN (1,3)
				ORDER BY cr.`is_main` DESC, c.`given_name` ASC";
		$contacts_data = $this->db->getall($sql);

		// 如果没有关联的联系人，则显示提示信息
		if (empty($contacts_data)) {
			return '<div style="padding: 30px; text-align: center; color: #666; background: #f8f9fa; border-radius: 6px; border: 1px dashed #ddd;">
				<i class="fa fa-users" style="font-size: 48px; color: #ccc; margin-bottom: 15px;"></i>
				<div style="font-size: 16px; margin-bottom: 8px;">暂无联系人信息</div>
				<div style="font-size: 14px; color: #999;">点击上方"添加联系人"按钮开始添加</div>
			</div>';
		}

		// 定义表格头部字符串，添加操作列
		$headstr = 'xuhaos,序号,center@given_name,姓名,left@mobile,手机号,left@position,职位,left@email,邮箱,left@honorific,称谓,left@is_main,主要联系人,center@actions,操作,center';

		// 格式化数据用于表格生成
		$formattedData = array();
		$index = 1;
		foreach ($contacts_data as $contact) {
			// 构建操作按钮
			$actions = '<div style="white-space: nowrap;">';
			$actions .= '<button type="button" class="btn btn-xs btn-info" onclick="editContact(' . $contact['id'] . ')" title="编辑">';
			$actions .= '<i class="fa fa-edit"></i></button> ';
			$actions .= '<button type="button" class="btn btn-xs btn-warning" onclick="toggleMainContact(' . $contact['id'] . ', ' . ($contact['is_main'] ? '0' : '1') . ')" title="' . ($contact['is_main'] ? '取消主要联系人' : '设为主要联系人') . '">';
			$actions .= '<i class="fa fa-star' . ($contact['is_main'] ? '' : '-o') . '"></i></button> ';
			$actions .= '<button type="button" class="btn btn-xs btn-danger" onclick="deleteContact(' . $contact['id'] . ')" title="删除">';
			$actions .= '<i class="fa fa-trash"></i></button>';
			$actions .= '</div>';

			$formattedData[] = array(
				'xuhaos'      => $index++,
				'given_name'  => htmlspecialchars($contact['given_name'] ?: '-'),
				'mobile'      => htmlspecialchars($contact['mobile'] ?: '-'),
				'position'    => htmlspecialchars($contact['position'] ?: '-'),
				'email'       => htmlspecialchars($contact['email'] ?: '-'),
				'honorific'   => htmlspecialchars($contact['honorific'] ?: '-'),
				'is_main'     => $contact['is_main'] ? '<span style="color: #28a745; font-weight: bold;"><i class="fa fa-star"></i> 是</span>' : '<span style="color: #6c757d;">否</span>',
				'actions'     => $actions
			);
		}

		// 使用系统插件生成表格
		$tableHtml = c('html')->createrows($formattedData, $headstr);

		// 添加操作相关的JavaScript
		$tableHtml .= $this->getContactActionsScript();

		return $tableHtml;
	}

	/**
	 * 获取联系人操作相关的JavaScript
	 * @return string
	 */
	private function getContactActionsScript()
	{
		return <<<SCRIPT
<script>
function editContact(contactId) {
    // TODO: 实现编辑联系人功能
    alert('编辑功能开发中...');
}

function toggleMainContact(contactId, isMain) {
    if (confirm(isMain ? '确定要设为主要联系人吗？' : '确定要取消主要联系人吗？')) {
        fetch('?m=customer&a=toggleMainContactAjax', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: 'contact_id=' + contactId + '&is_main=' + isMain + '&customer_id=' + customer_id
        })
        .then(response => response.json())
        .then(res => {
            if(res.success) {
                refreshContactTable();
            } else {
                alert(res.msg || '操作失败');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('网络错误，请重试');
        });
    }
}

function deleteContact(contactId) {
    if (confirm('确定要删除这个联系人吗？删除后无法恢复！')) {
        fetch('?m=customer&a=deleteContactAjax', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: 'contact_id=' + contactId + '&customer_id=' + customer_id
        })
        .then(response => response.json())
        .then(res => {
            if(res.success) {
                alert('删除成功');
                refreshContactTable();
            } else {
                alert(res.msg || '删除失败');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('网络错误，请重试');
        });
    }
}
</script>
SCRIPT;
	}

	/**
	 * 添加联系人Ajax处理
	 */
	public function addContactAjax()
	{
		try {
			// 获取并验证参数
			$customer_id = (int)$this->get('customer_id');
			$name = trim($this->get('given_name'));
			$mobile = trim($this->get('mobile'));
			$position = trim($this->get('position'));
			$email = trim($this->get('email'));
			$honorific = trim($this->get('honorific'));
			$is_main = (int)$this->get('is_main', 0);

			// 基础验证
			if (!$customer_id || !$name || !$mobile) {
				$this->returnjson(['success' => false, 'msg' => '姓名、手机号、客户ID不能为空']);
				return;
			}

			// 验证手机号格式
			if (!preg_match('/^1[3-9]\d{9}$/', $mobile)) {
				$this->returnjson(['success' => false, 'msg' => '请输入正确的手机号格式']);
				return;
			}

			// 验证邮箱格式（如果提供）
			if (!empty($email) && !filter_var($email, FILTER_VALIDATE_EMAIL)) {
				$this->returnjson(['success' => false, 'msg' => '请输入正确的邮箱格式']);
				return;
			}

			// 验证客户是否存在
			$customer = m('customer')->getone("id=$customer_id");
			if (!$customer) {
				$this->returnjson(['success' => false, 'msg' => '客户不存在']);
				return;
			}

			// 准备额外数据
			$extra_data = [
				'position' => $position,
				'email' => $email,
				'honorific' => $honorific
			];

			// 使用统一的联系人处理逻辑
			$result = m('contacts')->handleContactLogic($mobile, $name, 'customer', $customer_id, 0, $this, $extra_data);

			if (!$result['success']) {
				$this->returnjson(['success' => false, 'msg' => $result['message'] ?: '联系人保存失败']);
				return;
			}

			// 如果设置为主要联系人，需要更新关联表
			if ($is_main && $result['contact_id']) {
				$this->updateMainContact($result['contact_id'], $customer_id, true);
			}

			$this->returnjson(['success' => true, 'msg' => '联系人添加成功', 'contact_id' => $result['contact_id']]);

		} catch (Exception $e) {
			$this->returnjson(['success' => false, 'msg' => '系统错误：' . $e->getMessage()]);
		}
	}

	/**
	 * 切换主要联系人状态
	 */
	public function toggleMainContactAjax()
	{
		try {
			$contact_id = (int)$this->get('contact_id');
			$customer_id = (int)$this->get('customer_id');
			$is_main = (int)$this->get('is_main');

			if (!$contact_id || !$customer_id) {
				$this->returnjson(['success' => false, 'msg' => '参数错误']);
				return;
			}

			$result = $this->updateMainContact($contact_id, $customer_id, $is_main);

			if ($result) {
				$this->returnjson(['success' => true, 'msg' => $is_main ? '已设为主要联系人' : '已取消主要联系人']);
			} else {
				$this->returnjson(['success' => false, 'msg' => '操作失败']);
			}

		} catch (Exception $e) {
			$this->returnjson(['success' => false, 'msg' => '系统错误：' . $e->getMessage()]);
		}
	}

	/**
	 * 删除联系人
	 */
	public function deleteContactAjax()
	{
		try {
			$contact_id = (int)$this->get('contact_id');
			$customer_id = (int)$this->get('customer_id');

			if (!$contact_id || !$customer_id) {
				$this->returnjson(['success' => false, 'msg' => '参数错误']);
				return;
			}

			// 删除关联关系
			$rel_result = m('custcontrel')->delete("contact_id=$contact_id AND customer_id=$customer_id");

			// 检查该联系人是否还有其他关联，如果没有则删除联系人记录
			$other_relations = m('custcontrel')->rows("contact_id=$contact_id");
			if ($other_relations == 0) {
				m('contacts')->delete("id=$contact_id");
			}

			$this->returnjson(['success' => true, 'msg' => '联系人删除成功']);

		} catch (Exception $e) {
			$this->returnjson(['success' => false, 'msg' => '系统错误：' . $e->getMessage()]);
		}
	}

	/**
	 * 更新主要联系人状态
	 * @param int $contact_id 联系人ID
	 * @param int $customer_id 客户ID
	 * @param bool $is_main 是否为主要联系人
	 * @return bool
	 */
	private function updateMainContact($contact_id, $customer_id, $is_main)
	{
		$m = m('custcontrel');

		if ($is_main) {
			// 如果设为主要联系人，先将该客户的其他联系人设为非主要
			$m->update(['is_main' => 0], "customer_id=$customer_id AND rel_type IN (1,3)");
		}

		// 更新指定联系人的状态
		return $m->update(['is_main' => $is_main ? 1 : 0], "contact_id=$contact_id AND customer_id=$customer_id AND rel_type IN (1,3)");
	}
}
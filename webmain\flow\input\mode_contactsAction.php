<?php
/**
*	此文件是流程模块【contacts.联系人】对应控制器接口文件。
*/ 
class mode_contactsClassAction extends inputAction{
	
	/**
	*	重写函数：保存前处理，主要用于判断是否可以保存
	*	$table String 对应表名
	*	$arr Array 表单参数
	*	$id Int 对应表上记录Id 0添加时，大于0修改时
	*	$addbo Boolean 是否添加时
	*	return array('msg'=>'错误提示内容','rows'=> array()) 可返回空字符串，或者数组 rows 是可同时保存到数据库上数组
	*/
	protected function savebefore($table, $arr, $id, $addbo){
		// 编辑时，先删除旧的关联关系
		if (!$addbo && $id > 0) {
			$custid = isset($arr['custid']) ? (int)$arr['custid'] : 0;
			$projectid = isset($arr['projectid']) ? (int)$arr['projectid'] : 0;
			
			// 删除该联系人的所有关联关系（包括客户和项目）
			if ($custid > 0) {
				m('contacts')->removeContactRel($id, $custid); // 删除客户关联
			}
			if ($projectid > 0) {
				m('contacts')->removeContactRel($id, 0, $projectid); // 删除项目关联
			}
			
			// 如果没有指定具体的客户或项目，删除该联系人的所有关联
			if ($custid == 0 && $projectid == 0) {
				m('contacts')->removeContactRel($id); // 删除所有关联
			}
		}
		return ''; //确保返回空字符串或有效的数组
	}
	
	/**
	*	重写函数：保存后处理，主要保存其他表数据
	*	$table String 对应表名
	*	$arr Array 表单参数
	*	$id Int 对应表上记录Id
	*	$addbo Boolean 是否添加时
	*/	
	protected function saveafter($table, $arr, $id, $addbo){
		// 保存联系人后，处理与客户的关联
		$custid = isset($arr['custid']) ? (int)$arr['custid'] : 0;
		
		// 如果有客户ID，则添加关联关系
		if ($custid > 0) {
			// 调用 model 添加客户和联系人的关联
			// $contact_id, $customer_id, $is_main=1, $action=null
			$contactsModel = m('contacts');
			$contactsModel->addCustomerContactRel($id, $custid, 1, $this);
			
			// 可选：如果需要，更新项目联系人关联
			// $projectid = isset($arr['projectid']) ? (int)$arr['projectid'] : 0;
			// if ($projectid > 0) {
			// 	 $contactsModel->addProjectContactRel($id, $projectid, $custid, 1, $this);
			// }
		}
	}

    public function contactsNameData($where = ''){
        // 优先从GET参数获取custid，如果没有则从where参数获取
        $custid = (int)$this->get('custid', 0);
        $projectid = (int)$this->get('projectid', 0);
        
        // 如果GET参数中没有custid，尝试从where参数解析
        if($custid == 0 && !empty($where)){
            // where参数可能是字符串形式的custid
            $custid = (int)$where;
        }
        
        return m('contacts')->contactsNameData($custid, $projectid);
    }
	//读取所有的客户至text数组
	public function customerdata()
    {
        // 获取所有状态正常的客户数据，按更新时间倒序排列，unitname单独显示为12px字体
        $rows = m('customer')->getall('`status`=1','`id`,`name`,`unitname`','`optdt` desc');
        $arr = array();
        if($rows){
            foreach($rows as $k=>$rs){
                $arr[] = array(
                    'name' => $rs['name'],
                    'value' => $rs['id'],
                    'subname' => $rs['unitname'] // unitname作为subname单独返回，前端可控制12px字体显示
                );
            }
        } else {
            // 如果没有客户数据，返回提示信息
            $arr[] = array('value'=>0,'name'=>'没有可选择的客户');
        }
        return $arr;
    }
	
    /**
     * 获取联系人选择数据
     */
    public function getselectdata()
    {
        $custid = (int)$this->get('custid', 0);
        $projectid = (int)$this->get('projectid', 0);
        $act = $this->get('act', '');
        
        // 如果操作类型是获取联系人姓名数据
        if($act == 'contactsNameData'){
            // 工单任务：只获取项目下的联系人，不要客户联系人
            $contacts = [];
            
            if($projectid > 0) {
                $contacts = m('contacts')->getProjectContacts($projectid);
            }
            
            $arr = array();
            if(is_array($contacts)){
                foreach($contacts as $contact){
                    $arr[] = array(
                        'id' => $contact['id'],
                        'value' => $contact['id'],
                        'name' => $contact['given_name'] . ($contact['mobile'] ? ' (' . $contact['mobile'] . ')' : ''),
                        'given_name' => $contact['given_name'],
                        'given_name_original' => $contact['given_name'], // 为前端JS兼容
                        'mobile' => isset($contact['mobile']) ? $contact['mobile'] : '',
                        'honorific' => isset($contact['honorific']) ? $contact['honorific'] : '',
                        'position' => isset($contact['position']) ? $contact['position'] : '',
                        'is_main' => isset($contact['is_main']) ? $contact['is_main'] : 0,
                        'subname' => (isset($contact['mobile']) && !empty($contact['mobile'])) ? '手机:'.$contact['mobile'] : ''
                    );
                }
            }
            
            return array('rows' => $arr);
        }
        
        return array('rows' => array());
    }


}
			
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ajax连接测试</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border: 1px solid #ddd;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            border-radius: 8px;
        }
        .test-section {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #e0e0e0;
            background: #f9f9f9;
            border-radius: 5px;
        }
        .test-title {
            font-size: 16px;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
            border-bottom: 2px solid #007bff;
            padding-bottom: 5px;
        }
        .test-button {
            padding: 10px 20px;
            margin: 8px;
            border: 1px solid #007bff;
            background: #007bff;
            color: white;
            cursor: pointer;
            border-radius: 4px;
            font-size: 14px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .test-button:disabled {
            background: #6c757d;
            border-color: #6c757d;
            cursor: not-allowed;
        }
        .result-box {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
            max-height: 200px;
            overflow-y: auto;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }
        .fix-list {
            list-style: none;
            padding: 0;
        }
        .fix-list li {
            padding: 5px 0;
            border-bottom: 1px solid #eee;
        }
        .fix-list li:last-child {
            border-bottom: none;
        }
        .status-icon {
            margin-right: 8px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 style="text-align: center; color: #333; margin-bottom: 20px;">
            <i class="fa fa-plug"></i> Ajax连接测试工具
        </h1>

        <div class="test-section">
            <div class="test-title">
                <i class="fa fa-info-circle"></i> 问题说明
            </div>
            <p><strong>错误信息：</strong></p>
            <div class="result-box error">
Invalid JSON response: actionfile not exists;tpl_customer_addContactAjax.html not exists;
            </div>
            <p><strong>问题原因：</strong></p>
            <ul class="fix-list">
                <li><i class="fa fa-exclamation-triangle status-icon" style="color: #dc3545;"></i>系统尝试寻找不存在的action文件</li>
                <li><i class="fa fa-exclamation-triangle status-icon" style="color: #dc3545;"></i>系统尝试寻找不存在的模板文件</li>
                <li><i class="fa fa-exclamation-triangle status-icon" style="color: #dc3545;"></i>Ajax方法没有正确禁用模板显示</li>
            </ul>
        </div>

        <div class="test-section">
            <div class="test-title">
                <i class="fa fa-wrench"></i> 修复方案
            </div>
            <ul class="fix-list">
                <li><i class="fa fa-check status-icon" style="color: #28a745;"></i>在Ajax方法中添加 <code>$this->display = false;</code></li>
                <li><i class="fa fa-check status-icon" style="color: #28a745;"></i>使用 <code>ob_clean()</code> 清理输出缓冲区</li>
                <li><i class="fa fa-check status-icon" style="color: #28a745;"></i>设置正确的Content-Type头部</li>
                <li><i class="fa fa-check status-icon" style="color: #28a745;"></i>使用 <code>exit</code> 立即退出，避免额外输出</li>
            </ul>
        </div>

        <div class="test-section">
            <div class="test-title">
                <i class="fa fa-flask"></i> 连接测试
            </div>
            <p>点击下面的按钮测试Ajax连接是否正常：</p>
            
            <div style="text-align: center; margin: 20px 0;">
                <button class="test-button" onclick="testBasicConnection()">
                    <i class="fa fa-play"></i> 测试基础连接
                </button>
                <button class="test-button" onclick="testAddContact()">
                    <i class="fa fa-user-plus"></i> 测试添加联系人
                </button>
                <button class="test-button" onclick="testErrorHandling()">
                    <i class="fa fa-exclamation"></i> 测试错误处理
                </button>
            </div>

            <div id="testResults"></div>
        </div>

        <div class="test-section">
            <div class="test-title">
                <i class="fa fa-code"></i> 修复代码示例
            </div>
            <div class="result-box info">
// 修复前的Ajax方法
public function addContactAjax()
{
    ob_clean();
    header('Content-Type: application/json; charset=utf-8');
    // ... 处理逻辑
    echo json_encode(['success' => true, 'msg' => '成功']);
    exit;
}

// 修复后的Ajax方法
public function addContactAjax()
{
    // 禁用模板显示 - 关键修复
    $this->display = false;
    
    ob_clean();
    header('Content-Type: application/json; charset=utf-8');
    // ... 处理逻辑
    echo json_encode(['success' => true, 'msg' => '成功']);
    exit;
}
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">
                <i class="fa fa-list-check"></i> 测试检查项
            </div>
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
                <div>
                    <h4>后端测试</h4>
                    <ul class="fix-list">
                        <li><input type="checkbox" id="check1"> Ajax方法正常响应</li>
                        <li><input type="checkbox" id="check2"> 返回有效JSON格式</li>
                        <li><input type="checkbox" id="check3"> 没有额外的输出内容</li>
                        <li><input type="checkbox" id="check4"> 错误处理正常</li>
                    </ul>
                </div>
                <div>
                    <h4>前端测试</h4>
                    <ul class="fix-list">
                        <li><input type="checkbox" id="check5"> 能够正常解析JSON</li>
                        <li><input type="checkbox" id="check6"> 错误提示正确显示</li>
                        <li><input type="checkbox" id="check7"> 网络错误处理正常</li>
                        <li><input type="checkbox" id="check8"> 用户体验良好</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <script>
        function addResult(type, title, content) {
            const resultsDiv = document.getElementById('testResults');
            const resultBox = document.createElement('div');
            resultBox.className = `result-box ${type}`;
            resultBox.innerHTML = `<strong>${title}</strong>\n${content}`;
            resultsDiv.appendChild(resultBox);
        }

        function clearResults() {
            document.getElementById('testResults').innerHTML = '';
        }

        function testBasicConnection() {
            clearResults();
            addResult('info', '测试基础连接', '正在测试Ajax连接...');

            fetch('?m=customer&a=testAjax', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                }
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error('HTTP error! status: ' + response.status);
                }
                return response.text().then(text => {
                    try {
                        return JSON.parse(text);
                    } catch (e) {
                        console.error('Invalid JSON response:', text);
                        throw new Error('服务器返回了无效的响应格式:\n' + text);
                    }
                });
            })
            .then(res => {
                if (res && res.success) {
                    addResult('success', '✅ 连接测试成功', `消息: ${res.msg}\n时间: ${res.time}`);
                    document.getElementById('check1').checked = true;
                    document.getElementById('check2').checked = true;
                    document.getElementById('check3').checked = true;
                } else {
                    addResult('error', '❌ 连接测试失败', `错误: ${res && res.msg ? res.msg : '未知错误'}`);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                addResult('error', '❌ 连接测试失败', `错误: ${error.message}`);
            });
        }

        function testAddContact() {
            addResult('info', '测试添加联系人', '正在测试添加联系人接口...');

            const formData = new FormData();
            formData.append('customer_id', '1');
            formData.append('given_name', '测试联系人');
            formData.append('mobile', '13800138000');
            formData.append('position', '测试职位');
            formData.append('email', '<EMAIL>');

            fetch('?m=customer&a=addContactAjax', {
                method: 'POST',
                body: formData
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error('HTTP error! status: ' + response.status);
                }
                return response.text().then(text => {
                    try {
                        return JSON.parse(text);
                    } catch (e) {
                        console.error('Invalid JSON response:', text);
                        throw new Error('服务器返回了无效的响应格式:\n' + text);
                    }
                });
            })
            .then(res => {
                if (res && res.success !== undefined) {
                    if (res.success) {
                        addResult('success', '✅ 添加联系人测试成功', `消息: ${res.msg}`);
                    } else {
                        addResult('warning', '⚠️ 添加联系人测试返回业务错误', `错误: ${res.msg}`);
                    }
                    document.getElementById('check5').checked = true;
                    document.getElementById('check6').checked = true;
                } else {
                    addResult('error', '❌ 添加联系人测试失败', '响应格式不正确');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                addResult('error', '❌ 添加联系人测试失败', `错误: ${error.message}`);
            });
        }

        function testErrorHandling() {
            addResult('info', '测试错误处理', '正在测试错误处理机制...');

            // 测试无效参数
            fetch('?m=customer&a=addContactAjax', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: 'invalid_param=test'
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error('HTTP error! status: ' + response.status);
                }
                return response.text().then(text => {
                    try {
                        return JSON.parse(text);
                    } catch (e) {
                        console.error('Invalid JSON response:', text);
                        throw new Error('服务器返回了无效的响应格式:\n' + text);
                    }
                });
            })
            .then(res => {
                if (res && res.success === false) {
                    addResult('success', '✅ 错误处理测试成功', `正确返回错误: ${res.msg}`);
                    document.getElementById('check4').checked = true;
                    document.getElementById('check7').checked = true;
                    document.getElementById('check8').checked = true;
                } else {
                    addResult('warning', '⚠️ 错误处理测试异常', '应该返回错误但返回了成功');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                addResult('error', '❌ 错误处理测试失败', `错误: ${error.message}`);
            });
        }

        // 页面加载时自动运行基础连接测试
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Ajax连接测试页面已加载');
            setTimeout(testBasicConnection, 1000);
        });
    </script>
</body>
</html>

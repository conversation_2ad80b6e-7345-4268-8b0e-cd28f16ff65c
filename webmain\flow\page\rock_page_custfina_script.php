//<script>

c.initpage=function(){
	$('#key_{rand}').parent().before('<td><input onclick="js.datechange(this,\'month\')" style="width:110px;border-radius:0;border-left:0;'+datesss+'" placeholder="所属月份" readonly class="form-control" id="dt_{rand}" ></td>');
}
c.searchbtn=function(){
	var dt = get('dt_{rand}').value;
	this.search({month:dt});
}

if(pnum=='finall'){
	var zhangarr = [];
	c.createjizhang=function(d,d2){
		js.tanbody('optsotcks','['+d.type+']-'+d2.name+'',300,200,{
			html:'<div style="padding:10px">选择记账的财务帐号：<select id="optchangkude" class="form-control"><option value="">请选择...</option></select><br>说明：<textarea id="optchangkude1" class="form-control"></textarea></div>',
			btn:[{text:'生成'}]
		});
		js.setselectdata(get('optchangkude'), zhangarr, 'value');
		$('#optsotcks_btn0').click(function(){
			c.savejizhang(d);
		});
	}
	c.onloadbefore=function(d){
		if(d.zhangarr)zhangarr = d.zhangarr;
		a.settishi('<div class="tishi">此列表是财务人员显示已收付款的可快速生成记账单</div>');
	}
	c.savejizhang=function(d){
		var da = {accountid:get('optchangkude').value,sm:get('optchangkude1').value,id:d.id}
		if(!da.accountid){
			js.msgerror('请选择财务帐号');
			return;
		}
		js.tanclose('optsotcks');
		js.loading('生成中...');
		js.ajax(publicmodeurl('custfina','createjizhang'),da,function(ret){
			js.msgok('创建成功');
			a.reload();
		},'post,json');
	}
}else{
	c.createjizhang=function(d){
		js.msgerror('请在财务的菜单下操作');
	}
}	